import Image from 'next/image';
import React, { useState, useEffect, useImperativeHandle } from 'react';
import InformationBox from './InformationBox';
import FileUploadField from './FileUploadField';
import { Switch } from '@/components/ui/switch';
import {AcademicData, Proficiency} from '@/common';
import SectionLayout from './layout/SectionLayout';
import Publication from '@/app/assets/img/publication.png';
import { AcademicFieldProps, DocumentsRef } from '@/types';
import StudentsDocumentsLayout from './layout/StudentsDocumentsLayout';
import { useSaveDocumentsMutation, useGetDocumentsQuery } from '@/lib/redux/api/studentApi';
import { useToast } from '@/hooks/use-toast';

// Interface for child information
interface ChildInfo {
    id: string;
    name: string;
}

// Documents component interfaces
interface DocumentsProps {
    studentId?: string | number;
    initialData?: any;
    onSave?: (data: any) => void;
}

const Documents = React.forwardRef<DocumentsRef, DocumentsProps>(({ studentId, initialData, onSave }, ref) => {
    const [saveDocuments] = useSaveDocumentsMutation();
    const { toast } = useToast();
    const [fields, setFields] = useState<AcademicFieldProps[]>([
        { id: 'ssc', label: 'SSC', files: [] },
        { id: 'hsc', label: 'HSC', files: [] },
        { id: 'bachelor', label: 'Bachelor', files: [] },
        { id: 'masters', label: 'Masters', files: [] },
    ]);

    const [profileFields, setProfileFields] = useState<AcademicFieldProps[]>([
        { id: 'photo', label: 'Photo', files: [] },
        { id: 'passport', label: 'Passport', files: [] },
        { id: 'signature', label: 'Signature', files: [] },
    ]);

    const [proficiencyFields, setproficiencyFields] = useState<AcademicFieldProps[]>([
        { id: 'duolingo', label: 'Duolingo', files: [] },
        { id: 'ielts', label: 'IELTS', files: [] },
    ]);

    const [sponsorFields, setSponsorFields] = useState<AcademicFieldProps[]>([
        { id: 'photo', label: 'Photo', files: [] },
        { id: 'bank_statement', label: 'Bank Statement', files: [] },
    ]);

    // Dependents state
    const [hasDependents, setHasDependents] = useState(false);
    const [childrenInfo, setChildrenInfo] = useState<ChildInfo[]>([]);

    // Dependents file upload fields
    const [dependentsFields, setDependentsFields] = useState<AcademicFieldProps[]>([
        { id: 'main_dependent_photo', label: 'Photo', files: [] },
        { id: 'main_dependent_passport', label: 'Passport', files: [] },
    ]);

    // Children file upload fields - will be dynamically managed
    const [childrenFields, setChildrenFields] = useState<AcademicFieldProps[]>([]);

    // Effect to add first child when dependents is enabled
    useEffect(() => {
        if (hasDependents && childrenInfo.length === 0) {
            addChildInfo();
        } else if (!hasDependents) {
            setChildrenInfo([]);
            setChildrenFields([]);
        }
    }, [hasDependents]);

    // Effect to sync children fields with children info
    useEffect(() => {
        const newChildrenFields: AcademicFieldProps[] = [];
        childrenInfo.forEach((child) => {
            newChildrenFields.push(
                { id: `${child.id}_photo`, label: `Child Photo`, files: [] },
                { id: `${child.id}_passport`, label: `Child Passport`, files: [] }
            );
        });
        setChildrenFields(newChildrenFields);
    }, [childrenInfo]);

    // Functions for handling child information
    const addChildInfo = () => {
        const newChild: ChildInfo = {
            id: `child-${Date.now()}`,
            name: '',
        };
        setChildrenInfo([...childrenInfo, newChild]);
    };

    const removeChildInfo = (childId: string) => {
        setChildrenInfo(childrenInfo.filter(child => child.id !== childId));
    };

    const updateChildInfo = (childId: string, field: keyof ChildInfo, value: any) => {
        setChildrenInfo(childrenInfo.map(child =>
            child.id === childId ? { ...child, [field]: value } : child
        ));
    };

    // Form submission handler
    const transformToApiFormat = () => {
        const formData = new FormData();

        // Add academic sections
        const academicSections = fields.filter(field => field.files.length > 0).map(field => field.id);
        formData.append('academicSections', JSON.stringify(academicSections));

        // Add proficiency sections
        const proficiencySections = proficiencyFields.filter(field => field.files.length > 0).map(field => field.id);
        formData.append('proficiencySections', JSON.stringify(proficiencySections));

        // Add sponsor name (you might want to make this dynamic)
        formData.append('sponsorName', 'Philip Carter');

        // Add dependents info
        formData.append('takeDependents', hasDependents.toString());

        if (hasDependents) {
            // Add main dependent info (John Grey)
            const dependents = [{ name: 'John Grey', passport: '********' }];
            formData.append('dependents', JSON.stringify(dependents));

            // Add children info
            const children = childrenInfo.map(child => ({
                name: child.name,
                passport: 'Y7654321' // You might want to extract passport number from uploaded files
            }));
            formData.append('children', JSON.stringify(children));
        }

        // Add files
        fields.forEach(field => {
            field.files.forEach(file => {
                formData.append(field.id, file);
            });
        });

        proficiencyFields.forEach(field => {
            field.files.forEach(file => {
                formData.append(field.id, file);
            });
        });

        sponsorFields.forEach(field => {
            field.files.forEach(file => {
                formData.append(`sponsor_${field.id}`, file);
            });
        });

        // Add dependents files
        if (hasDependents) {
            dependentsFields.forEach(field => {
                field.files.forEach(file => {
                    formData.append(`dependent_${field.id}`, file);
                });
            });

            // Add children files
            childrenFields.forEach(field => {
                field.files.forEach(file => {
                    formData.append(`child_${field.id}`, file);
                });
            });
        }

        return formData;
    };

    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
        saveForm: async () => {
            try {
                if (!studentId) {
                    return { success: false, error: "Student ID is required" };
                }

                const formData = transformToApiFormat();
                const result = await saveDocuments({
                    studentId: studentId,
                    documentsData: formData
                }).unwrap();

                if (result.success) {
                    toast({
                        title: "Success",
                        description: "Documents saved successfully!",
                    });
                    if (onSave) {
                        onSave(result.data);
                    }
                    return { success: result.success, data: result.data };
                }
                return { success: false, error: "Failed to save documents" };
            } catch (error: any) {
                console.error('Error saving documents:', error);
                toast({
                    title: "Error",
                    description: error?.data?.message || "Failed to save documents",
                    variant: "destructive",
                });
                return { success: false, error: error?.data?.message || "Failed to save documents" };
            }
        },
    }));

    // const tableHead = ['Title', 'Document', 'Actions'];
    // const handleUpload = (index: number) => {
    //     // Handle file upload logic here
    //     // console.log(`Upload clicked for row ${index}`);
    //   };
    
    //   const handleView = (index: number) => {
    //     console.log(`View clicked for row ${index}`);
    //   };
    
    //   const handleEdit = (index: number) => {
    //     console.log(`Edit clicked for row ${index}`);
    //   };
    return (
        <SectionLayout  heading={'Documents Form'}>
            <StudentsDocumentsLayout sectionTitle='Profile'>
                <FileUploadField 
                    fields={profileFields} 
                    setFields={setProfileFields} 
                />
            </StudentsDocumentsLayout>
            <StudentsDocumentsLayout className={'mt-[30px]'}  sectionTitle='Academic'>
                <FileUploadField 
                    fields={fields} 
                    setFields={setFields} 
                />
            </StudentsDocumentsLayout>
            <StudentsDocumentsLayout 
                className={'mt-[30px]'} 
                sectionTitle='Proficiency'
            >
                <FileUploadField 
                    fields={proficiencyFields} 
                    setFields={setproficiencyFields} 
                />
            </StudentsDocumentsLayout>
            <StudentsDocumentsLayout 
                className={'mt-[30px]'} 
                sectionTitle='Sponsor'
            >
                <div className='flex flex-col md:flex-row mb-[18px]'>
                    <label className='w-[15%] block text-base font-medium mb-2 text-grayFive'>Name</label>
                    <span className='mt-0.5 w-[75%] font-semibold text-base leading-5 text-grayFive'>Phillip Carter</span>
                </div>
                <FileUploadField 
                    fields={sponsorFields} 
                    setFields={setSponsorFields} 
                />
            </StudentsDocumentsLayout>
            {/* Dependents Section */}
            <div className='mt-[30px] bg-white rounded-lg border border-gray-200 p-6'>
                {/* Switch Section */}
                <div className='flex items-center gap-3 mb-6'>
                    <span className='font-medium text-base text-grayFive'>Do you want to take dependents or not?</span>
                    <Switch
                        checked={hasDependents}
                        onCheckedChange={setHasDependents}
                        id='dependents-switch'
                    />
                </div>

                {hasDependents && (
                    <div className='space-y-8'>
                        {/* Main Dependents Section */}
                        <div>
                            <div className='flex items-center gap-2 mb-6'>
                                <h3 className='font-semibold text-xl text-graySix'>Dependents</h3>
                                <span className='text-xs text-gray-500'>*Accepted file types: JPEG, PNG, PDF, up to 50 MB</span>
                            </div>

                            {/* Main Dependent Form */}
                            <div className='space-y-6'>
                                {/* Name Field */}
                                <div className='flex flex-col md:flex-row items-start md:items-center gap-3'>
                                    <label className='w-full md:w-[15%] block text-base font-medium text-grayFive'>Name</label>
                                    <span className='w-full md:w-[75%] font-semibold text-base leading-5 text-grayFive bg-gray-50 px-4 py-3 rounded-lg'>John Grey</span>
                                </div>

                                {/* File Upload Fields */}
                                <FileUploadField
                                    fields={dependentsFields}
                                    setFields={setDependentsFields}
                                />
                            </div>
                        </div>

                        {/* Children Section */}
                        {childrenInfo.map((child, index) => (
                            <div key={child.id}>
                                <div className='flex items-center justify-between mb-6'>
                                    <div className='flex items-center gap-2'>
                                        <h3 className='font-semibold text-xl text-graySix'>Child {index + 1}</h3>
                                        <span className='text-xs text-gray-500'>*Accepted file types: JPEG, PNG, PDF, up to 50 MB</span>
                                    </div>
                                    {childrenInfo.length > 1 && (
                                        <button
                                            type="button"
                                            onClick={() => removeChildInfo(child.id)}
                                            className="text-red-500 hover:text-red-700 text-sm font-medium"
                                        >
                                            Remove
                                        </button>
                                    )}
                                </div>

                                <div className='space-y-6'>
                                    {/* Child Name Field */}
                                    <div className='flex flex-col md:flex-row items-start md:items-center gap-3'>
                                        <label className='w-full md:w-[15%] block text-base font-medium text-grayFive'>Name</label>
                                        <input
                                            type="text"
                                            value={child.name}
                                            onChange={(e) => updateChildInfo(child.id, 'name', e.target.value)}
                                            placeholder="Type child name"
                                            className='w-full md:w-[75%] px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500'
                                        />
                                    </div>

                                    {/* Child File Upload Fields */}
                                    <FileUploadField
                                        fields={childrenFields.filter(field => field.id.startsWith(child.id))}
                                        setFields={setChildrenFields}
                                    />
                                </div>
                            </div>
                        ))}

                        {/* Add Child Info Button */}
                        <div className='flex justify-start'>
                            <button
                                type="button"
                                onClick={addChildInfo}
                                className="flex items-center gap-2 text-blue-500 hover:text-blue-600 font-medium"
                            >
                                <span className="text-lg">+</span>
                                Add Child Info
                            </button>
                        </div>
                    </div>
                )}
            </div>
            {/* Educations data */}
            {/* <h2 className='font-semibold text-xl text-graySix'>Academic</h2>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                {AcademicData.map((data, index) => (
                    <InformationBox key={index} data={data} />
                ))}
            </div>
            <h2 className='mt-6 font-semibold text-xl text-graySix'>Proficiency</h2>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                {Proficiency.map((data, index) => (
                    <InformationBox key={index} data={data} />
                ))}
            </div>

            <h2 className='mt-6 font-semibold text-xl text-graySix'>Publication</h2>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div className='mt-6 border rounded-lg py-4 px-5 border-[#1952BB] border-opacity-20'>
                    <div className='flex justify-between items-center'>
                        <div className='flex flex-col gap-2.5'>
                            <div className='flex items-center gap-4'>
                                <span className='font-medium text-lg text-graySix'>Marine Biology, Climate Change</span>
                                <span className='font-medium text-xs text-primaryColor bg-primaryOne rounded-[16px] py-[2px] px-2'>Feb 12, 2023</span>
                            </div>
                            <p className='font-normal text-xs leading-5 tracking-[0.25px] text-graySix'>International Journal of Business and Management Studies</p>
                        </div>
                        <div>
                            <Image src={Publication} alt="Publications logo" />
                        </div>
                    </div>
                </div>
                <div className='mt-6 border rounded-lg py-4 px-5 border-[#1952BB] border-opacity-20'>
                    <div className='flex justify-between items-center'>
                        <div className='flex flex-col gap-2.5'>
                            <div className='flex items-center gap-4'>
                                <span className='font-medium text-lg text-graySix'>Marine Biology, Climate Change</span>
                                <span className='font-medium text-xs text-primaryColor bg-primaryOne rounded-[16px] py-[2px] px-2'>Feb 12, 2023</span>
                            </div>
                            <p className='font-normal text-xs leading-5 tracking-[0.25px] text-graySix'>International Journal of Business and Management Studies</p>
                        </div>
                        <div>
                            <Image src={Publication} alt="Publications logo" />
                        </div>
                    </div>
                </div>
            </div> */}
            {/* <Table>
                <TableHeader>
                    <TableRow>
                        {tableHead.map((th, index) => (
                            <TableHead key={index} className='bg-white px-6 py-3.5 font-bold text-xs tracking-[0.4px] text-grayFive'>{th}</TableHead>
                        ))}
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {data.map((item, index) => (
                        <TableRow className='hover:bg-primaryFour' key={index}>
                            <TableCell 
                                className="px-6 py-3.5 font-normal text-xs tracking-[0.4px] text-graySix"
                            >
                                {item.name}
                            </TableCell>
                            <TableCell 
                                className='px-6 py-3.5 flex items-center gap-2.5'
                            >
                                 {item.files && (
                                    <div className="flex flex-wrap gap-2 mt-1">
                                    {item.files.map((file, i) => (
                                        <span
                                        key={i}
                                        className="bg-primaryOne text-[#144296] px-2 py-1 rounded-[50px] text-xs flex items-center gap-1"
                                        >
                                        {file} <button onClick={() => console.log(`Remove ${file}`)}><X_icon className='text-[#144296]' /></button>
                                        </span>
                                    ))}
                                    </div>
                                )}
                            </TableCell>
                            <TableCell 
                                className="px-6 py-3.5 font-normal text-xs tracking-[0.4px] text-graySix"
                            >
                                <div className="flex justify-left gap-2">
                                    <Button onClick={() => handleUpload(index)} size="sm" >
                                        <Edit />
                                    </Button>
                                    <Button onClick={() => handleEdit(index)} size="sm">
                                        <Download />
                                    </Button>
                                    <Button onClick={() => handleUpload(index)} size="sm">
                                        <Upload className='text-primaryColor' />
                                    </Button>
                                    <Button onClick={() => handleView(index)} size="sm">
                                        <Visibility className="text-primaryColor" />
                                    </Button>
                                    </div>
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table> */}
        </SectionLayout>
    );
});

Documents.displayName = 'Documents';

export default Documents;